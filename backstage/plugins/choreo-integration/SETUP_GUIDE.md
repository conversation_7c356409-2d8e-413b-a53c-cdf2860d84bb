# Choreo Integration Plugin - Setup Guide

This guide will walk you through setting up the Choreo Integration plugin in your Backstage instance.

## Prerequisites

1. A running Backstage instance
2. Access to [Choreo Console](https://console.choreo.dev)
3. Node.js and yarn/npm installed

## Step 1: Configure Choreo OAuth Application

### 1.1 Create OAuth Application in Choreo

1. Log in to [Choreo Console](https://console.choreo.dev)
2. Navigate to **Settings** → **API Credentials**
3. Click **Create** to create a new OAuth application
4. Fill in the application details:
   - **Name**: `Backstage Integration`
   - **Description**: `OAuth application for Backstage plugin integration`
   - **Application Type**: `Single Page Application (SPA)`
   - **Grant Types**: Select `Authorization Code` and `Refresh Token`

### 1.2 Configure Redirect URIs

Add the following redirect URIs (adjust the domain for your environment):

**For Development:**
```
http://localhost:3000/choreo-integration/auth/callback
```

**For Production:**
```
https://your-backstage-domain.com/choreo-integration/auth/callback
```

### 1.3 Configure Allowed Origins

Add the following allowed origins:

**For Development:**
```
http://localhost:3000
```

**For Production:**
```
https://your-backstage-domain.com
```

### 1.4 Note the Client ID

After creating the application, copy the **Client ID** - you'll need this for configuration.

## Step 2: Configure Environment Variables

### 2.1 Add Environment Variable

Add the following to your Backstage environment configuration:

**Option A: Using .env file**
```bash
# In your Backstage root directory, create or edit .env
REACT_APP_CHOREO_CLIENT_ID=your-choreo-client-id-here
```

**Option B: Using app-config.yaml**
```yaml
# In app-config.yaml
app:
  choreo:
    clientId: ${CHOREO_CLIENT_ID}
```

Then set the environment variable:
```bash
export CHOREO_CLIENT_ID=your-choreo-client-id-here
```

## Step 3: Install and Configure the Plugin

### 3.1 Install Dependencies

The plugin is already included in your workspace. If you need to install it separately:

```bash
cd packages/app
yarn add @internal/plugin-choreo-integration
```

### 3.2 Add Routes to App

Edit `packages/app/src/App.tsx` and add the plugin routes:

```typescript
import { 
  ChoreoIntegrationPage, 
  ChoreoAuthCallbackPage 
} from '@internal/plugin-choreo-integration';

// Add these routes inside your <Routes> component
<Route path="/choreo-integration" element={<ChoreoIntegrationPage />} />
<Route path="/choreo-integration/auth/callback" element={<ChoreoAuthCallbackPage />} />
```

### 3.3 Add Navigation (Optional)

Edit `packages/app/src/components/Root/Root.tsx` to add navigation:

```typescript
import { SidebarItem } from '@backstage/core-components';
import CloudIcon from '@material-ui/icons/Cloud'; // Or use a custom Choreo icon

// Add this inside your <Sidebar> component
<SidebarItem icon={CloudIcon} to="choreo-integration" text="Choreo" />
```

## Step 4: Start Backstage

### 4.1 Start the Development Server

```bash
# From your Backstage root directory
yarn dev
```

### 4.2 Access the Plugin

Navigate to: `http://localhost:3000/choreo-integration`

## Step 5: Test Authentication

### 5.1 Initial Access

1. Navigate to the Choreo Integration page
2. You should see the authentication UI
3. Click "Sign in to Choreo"

### 5.2 Authentication Flow

1. A popup window should open with the Choreo login page
2. Sign in with your Choreo credentials
3. Grant permissions to the application
4. The popup should close automatically
5. You should now see your user information and authenticated state

### 5.3 Verify Token Storage

1. Open browser developer tools
2. Go to Application → Local Storage
3. You should see encrypted token data stored securely

## Step 6: Using the API Client

### 6.1 Example Usage

Create a component that uses the Choreo API:

```typescript
import React, { useState } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { 
  choreoAuthApiRef, 
  DefaultChoreoApiClient,
  ChoreoAuthGuard 
} from '@internal/plugin-choreo-integration';

export const ChoreoProjectsList = () => {
  const choreoAuthApi = useApi(choreoAuthApiRef);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);

  const apiClient = new DefaultChoreoApiClient({
    baseUrl: 'https://api.choreo.dev/v1', // Adjust based on actual Choreo API
    authApi: choreoAuthApi,
  });

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const data = await apiClient.get('/projects');
      setProjects(data);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ChoreoAuthGuard>
      <div>
        <button onClick={fetchProjects} disabled={loading}>
          {loading ? 'Loading...' : 'Fetch Projects'}
        </button>
        <ul>
          {projects.map((project: any) => (
            <li key={project.id}>{project.name}</li>
          ))}
        </ul>
      </div>
    </ChoreoAuthGuard>
  );
};
```

## Troubleshooting

### Common Issues

**1. "Failed to open authentication popup"**
- **Cause**: Popup blockers are enabled
- **Solution**: Allow popups for your Backstage domain

**2. "Invalid redirect URI"**
- **Cause**: Redirect URI mismatch between Choreo config and plugin
- **Solution**: Ensure the redirect URI in Choreo exactly matches your plugin configuration

**3. "Client ID not found"**
- **Cause**: Environment variable not set correctly
- **Solution**: Verify `REACT_APP_CHOREO_CLIENT_ID` is set and restart Backstage

**4. "Network error during authentication"**
- **Cause**: Network connectivity issues or CORS problems
- **Solution**: Check network connectivity and ensure Choreo endpoints are accessible

**5. "Token refresh failed"**
- **Cause**: Refresh token expired or invalid
- **Solution**: User needs to sign in again

### Debug Mode

Enable debug logging by setting:

```bash
DEBUG=choreo-integration:*
```

### Verify Configuration

Check that your configuration is correct:

```typescript
// In browser console
console.log(process.env.REACT_APP_CHOREO_CLIENT_ID);
```

## Security Considerations

1. **Never commit client secrets** - The plugin uses public OAuth flow (PKCE)
2. **Use HTTPS in production** - Ensure your Backstage instance uses HTTPS
3. **Validate redirect URIs** - Only add necessary redirect URIs in Choreo
4. **Monitor token usage** - Regularly review OAuth application usage in Choreo

## Next Steps

Once authentication is working:

1. Explore the Choreo API documentation
2. Build custom components using the `ChoreoApiClient`
3. Integrate with Backstage catalog entities
4. Add custom dashboards and visualizations

## Support

For issues related to:
- **Plugin functionality**: Check the plugin documentation and GitHub issues
- **Choreo API**: Refer to Choreo documentation and support channels
- **Backstage integration**: Consult Backstage documentation and community
