# Choreo Integration Plugin - Setup Guide

This guide will walk you through setting up the Choreo Integration plugin in your Backstage instance.

## Prerequisites

1. A running Backstage instance
2. Access to [Choreo Console](https://console.choreo.dev)
3. Node.js and yarn/npm installed

## Step 1: Configure Choreo OAuth Application

### 1.1 Create OAuth Application in Choreo

1. Go to the [Choreo Developer Portal](https://devportal.choreo.dev) and sign in
2. In the Developer Portal header, click **Applications** and then click **+Create**
3. Fill in the application details:
   - **Name**: `Backstage Integration`
   - **Description**: `OAuth application for Backstage plugin integration`
4. Click **Create** to create the application

### 1.2 Generate OAuth Credentials

1. After creating the application, you'll be taken to the **Application Overview** page
2. In the left navigation menu, click the desired environment under **Credentials** (e.g., **Development** for local testing)
3. This opens the **Application Keys** pane for that environment
4. Expand **Advanced configurations** and configure the following:
   - **<PERSON> types**: Select `Authorization Code` and `Refresh Token`
   - **Public client**: Enable **Allow authentication without the client secret** (required for Single Page Applications)
   - **PKCE for enhanced security**: Set to **Mandatory** (recommended for security)
   - **Application access token expiry time**: Set to `3600` seconds (1 hour) or as needed
   - **Refresh token expiry time**: Set to `86400` seconds (24 hours) or as needed
5. Click **Generate Credentials**
6. The **Application Keys** pane will display the **Consumer Key** and **Consumer Secret**

### 1.3 Configure Redirect URIs

**Important Note**: The current Choreo Developer Portal interface may not show explicit redirect URI configuration fields during application creation. However, the OAuth application needs to be configured to accept the following redirect URIs:

**For Development:**
```
http://localhost:3000/choreo-integration/auth/callback
```

**For Production:**
```
https://your-backstage-domain.com/choreo-integration/auth/callback
```

**If you encounter redirect URI errors during testing:**
1. Contact Choreo support to configure the redirect URIs for your OAuth application
2. Or check if there are additional configuration options in the Developer Portal that allow setting redirect URIs
3. Ensure your Choreo organization settings allow the domains you're using

### 1.4 Note the Consumer Key

After generating credentials, copy the **Consumer Key** (this acts as your Client ID) - you'll need this for configuration.

## Step 2: Configure Environment Variables

### 2.1 Add Environment Variable

Add the following to your Backstage environment configuration:

**Option A: Using .env file**
```bash
# In your Backstage root directory, create or edit .env
REACT_APP_CHOREO_CLIENT_ID=your-choreo-client-id-here
```

**Option B: Using app-config.yaml**
```yaml
# In app-config.yaml
app:
  choreo:
    clientId: ${CHOREO_CLIENT_ID}
```

Then set the environment variable:
```bash
export CHOREO_CLIENT_ID=your-choreo-client-id-here
```

## Step 3: Install and Configure the Plugin

### 3.1 Install Dependencies

The plugin is already included in your workspace. If you need to install it separately:

```bash
cd packages/app
yarn add @internal/plugin-choreo-integration
```

### 3.2 Add Routes to App

Edit `packages/app/src/App.tsx` and add the plugin routes:

```typescript
import { 
  ChoreoIntegrationPage, 
  ChoreoAuthCallbackPage 
} from '@internal/plugin-choreo-integration';

// Add these routes inside your <Routes> component
<Route path="/choreo-integration" element={<ChoreoIntegrationPage />} />
<Route path="/choreo-integration/auth/callback" element={<ChoreoAuthCallbackPage />} />
```

### 3.3 Add Navigation (Optional)

Edit `packages/app/src/components/Root/Root.tsx` to add navigation:

```typescript
import { SidebarItem } from '@backstage/core-components';
import CloudIcon from '@material-ui/icons/Cloud'; // Or use a custom Choreo icon

// Add this inside your <Sidebar> component
<SidebarItem icon={CloudIcon} to="choreo-integration" text="Choreo" />
```

## Step 4: Start Backstage

### 4.1 Start the Development Server

```bash
# From your Backstage root directory
yarn dev
```

### 4.2 Access the Plugin

Navigate to: `http://localhost:3000/choreo-integration`

## Step 5: Test Authentication

### 5.1 Initial Access

1. Navigate to the Choreo Integration page
2. You should see the authentication UI
3. Click "Sign in to Choreo"

### 5.2 Authentication Flow

1. A popup window should open with the Choreo login page
2. Sign in with your Choreo credentials
3. Grant permissions to the application
4. The popup should close automatically
5. You should now see your user information and authenticated state

### 5.3 Verify Token Storage

1. Open browser developer tools
2. Go to Application → Local Storage
3. You should see encrypted token data stored securely

## Step 6: Using the API Client

### 6.1 Example Usage

Create a component that uses the Choreo API:

```typescript
import React, { useState } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { 
  choreoAuthApiRef, 
  DefaultChoreoApiClient,
  ChoreoAuthGuard 
} from '@internal/plugin-choreo-integration';

export const ChoreoProjectsList = () => {
  const choreoAuthApi = useApi(choreoAuthApiRef);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);

  const apiClient = new DefaultChoreoApiClient({
    baseUrl: 'https://api.choreo.dev/v1', // Adjust based on actual Choreo API
    authApi: choreoAuthApi,
  });

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const data = await apiClient.get('/projects');
      setProjects(data);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ChoreoAuthGuard>
      <div>
        <button onClick={fetchProjects} disabled={loading}>
          {loading ? 'Loading...' : 'Fetch Projects'}
        </button>
        <ul>
          {projects.map((project: any) => (
            <li key={project.id}>{project.name}</li>
          ))}
        </ul>
      </div>
    </ChoreoAuthGuard>
  );
};
```

## Troubleshooting

### Common Issues

**1. "Failed to open authentication popup"**
- **Cause**: Popup blockers are enabled
- **Solution**: Allow popups for your Backstage domain

**2. "Invalid redirect URI"**
- **Cause**: Redirect URI mismatch between Choreo config and plugin
- **Solution**: Ensure the redirect URI in Choreo exactly matches your plugin configuration

**3. "Client ID not found"**
- **Cause**: Environment variable not set correctly
- **Solution**: Verify `REACT_APP_CHOREO_CLIENT_ID` is set with the Consumer Key from Choreo Developer Portal and restart Backstage

**4. "Invalid redirect URI" or "redirect_uri_mismatch"**
- **Cause**: Redirect URI not configured in Choreo OAuth application
- **Solution**:
  - Check if Choreo Developer Portal has redirect URI configuration options
  - Contact Choreo support to configure redirect URIs for your application
  - Ensure the redirect URI exactly matches: `http://localhost:3000/choreo-integration/auth/callback`

**5. "Network error during authentication"**
- **Cause**: Network connectivity issues or CORS problems
- **Solution**: Check network connectivity and ensure Choreo endpoints are accessible

**6. "Token refresh failed"**
- **Cause**: Refresh token expired or invalid
- **Solution**: User needs to sign in again

**7. "OAuth application not found in Developer Portal"**
- **Cause**: Choreo interface has changed from Console to Developer Portal
- **Solution**:
  - Use [Choreo Developer Portal](https://devportal.choreo.dev) instead of Console
  - Create applications under **Applications** section, not in Console settings
  - Generate credentials in the Developer Portal environment-specific sections

### Debug Mode

Enable debug logging by setting:

```bash
DEBUG=choreo-integration:*
```

### Verify Configuration

Check that your configuration is correct:

```typescript
// In browser console
console.log(process.env.REACT_APP_CHOREO_CLIENT_ID);
```

## Security Considerations

1. **Never commit client secrets** - The plugin uses public OAuth flow (PKCE)
2. **Use HTTPS in production** - Ensure your Backstage instance uses HTTPS
3. **Validate redirect URIs** - Only add necessary redirect URIs in Choreo
4. **Monitor token usage** - Regularly review OAuth application usage in Choreo

## Next Steps

Once authentication is working:

1. Explore the Choreo API documentation
2. Build custom components using the `ChoreoApiClient`
3. Integrate with Backstage catalog entities
4. Add custom dashboards and visualizations

## Support

For issues related to:
- **Plugin functionality**: Check the plugin documentation and GitHub issues
- **Choreo API**: Refer to Choreo documentation and support channels
- **Backstage integration**: Consult Backstage documentation and community
