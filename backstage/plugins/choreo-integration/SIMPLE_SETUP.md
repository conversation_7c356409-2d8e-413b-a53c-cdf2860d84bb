# Choreo Integration Plugin - Simple PAT Setup

This guide shows how to set up the Choreo integration plugin using **Personal Access Tokens (PATs)** - a much simpler approach than OAuth 2.0.

## 🚀 Quick Setup (5 minutes)

### Step 1: Create a Personal Access Token

1. Go to [Choreo Console → Account Settings → Tokens](https://console.choreo.dev/account/settings/token)
2. Click **"Create Token"** or **"Generate New Token"**
3. Give your token a descriptive name: `Backstage Integration`
4. Select appropriate scopes/permissions for your needs
5. Copy the generated token (save it securely - you won't see it again!)

### Step 2: Configure Backstage

Add the token to your Backstage environment:

**For Development (.env file):**
```bash
REACT_APP_CHOREO_PAT=your-personal-access-token-here
```

**For Production (environment variables):**
```bash
export REACT_APP_CHOREO_PAT=your-personal-access-token-here
```

### Step 3: Add Plugin to Backstage

**In your `packages/app/src/App.tsx`:**
```typescript
import { ChoreoSimpleIntegrationPage } from '@internal/plugin-choreo-integration';

// Add to your routes
<Route path="/choreo" element={<ChoreoSimpleIntegrationPage />} />
```

**In your `packages/app/package.json`:**
```json
{
  "dependencies": {
    "@internal/plugin-choreo-integration": "^0.1.0"
  }
}
```

### Step 4: Test the Integration

1. Start your Backstage app: `yarn start`
2. Navigate to `/choreo` in your browser
3. You should see your Choreo projects and connection status

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REACT_APP_CHOREO_PAT` | Your Personal Access Token | Required |
| `REACT_APP_CHOREO_API_BASE_URL` | Choreo API base URL | `https://api.choreo.dev/v1` |

### API Usage Example

```typescript
import { useApi } from '@backstage/core-plugin-api';
import { choreoPatApiRef } from '@internal/plugin-choreo-integration';

const MyComponent = () => {
  const choreoApi = useApi(choreoPatApiRef);
  
  const fetchProjects = async () => {
    try {
      const projects = await choreoApi.get('/projects');
      console.log('Projects:', projects);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    }
  };
  
  // ... rest of component
};
```

## ✅ Advantages of PAT Approach

- **Simple**: No OAuth flows, popups, or complex authentication
- **Fast setup**: 5-minute configuration vs hours of OAuth setup
- **Reliable**: No token refresh logic or authentication state management
- **Internal-friendly**: Perfect for internal Backstage instances
- **Consistent access**: All users get the same level of access

## 🔒 Security Considerations

- **Token storage**: Store PATs securely in environment variables
- **Token rotation**: Regularly rotate your PATs
- **Scope limitation**: Only grant necessary permissions to your PAT
- **Access control**: Ensure only authorized users can access your Backstage instance

## 🆚 PAT vs OAuth 2.0 Comparison

| Feature | PAT Approach | OAuth 2.0 Approach |
|---------|--------------|-------------------|
| Setup complexity | ⭐ Simple | ⭐⭐⭐ Complex |
| User experience | ⭐⭐⭐ Seamless | ⭐⭐ Requires login |
| Individual user auth | ❌ No | ✅ Yes |
| Internal tools | ⭐⭐⭐ Perfect | ⭐⭐ Overkill |
| Token management | ⭐⭐⭐ Simple | ⭐ Complex |
| Security | ⭐⭐ Good | ⭐⭐⭐ Better |

## 🛠️ Troubleshooting

### "PAT not configured" error
- Check that `REACT_APP_CHOREO_PAT` is set correctly
- Restart your Backstage application after setting the environment variable

### "API request failed" errors
- Verify your PAT is valid and not expired
- Check that your PAT has the necessary scopes/permissions
- Ensure the Choreo API base URL is correct

### "No projects found"
- Verify your PAT has access to the projects you expect
- Check the API endpoint being called matches Choreo's actual API structure

## 📚 Next Steps

1. **Customize the UI**: Modify `ChoreoSimplePage.tsx` to show the data you need
2. **Add more API calls**: Use the `choreoPatApiRef` to call other Choreo APIs
3. **Integrate with Backstage catalog**: Show Choreo info on entity pages
4. **Add monitoring**: Implement health checks and error monitoring

## 🤝 Need Help?

- Check the [Choreo API documentation](https://docs.choreo.dev)
- Review your PAT permissions in Choreo Console
- Verify environment variables are set correctly
