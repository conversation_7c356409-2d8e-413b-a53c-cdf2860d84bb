export {
  choreoIntegrationPlugin,
  ChoreoIntegrationPage,
  ChoreoAuthCallbackPage
} from './plugin';

// Export API types and utilities for external use
export type {
  ChoreoAuthApi,
  ChoreoTokens,
  ChoreoUserInfo,
  ChoreoAuthState,
  ChoreoApiClient,
} from './api';

export {
  choreoAuthApiRef,
  ChoreoAuthError,
  ChoreoTokenExpiredError,
  ChoreoAuthenticationError,
  ChoreoNetworkError,
} from './api';

// Export components for external use
export { ChoreoAuthProvider, useChoreoAuth } from './components/ChoreoAuthProvider';
export { ChoreoAuthButton } from './components/ChoreoAuthButton';
export { ChoreoAuthGuard } from './components/ChoreoAuthGuard';
export { ChoreoUserInfo } from './components/ChoreoUserInfo';
export { ChoreoAuthCallback } from './components/ChoreoAuthCallback';
