import { Observable, BehaviorSubject } from '@backstage/types';
import { <PERSON>rror<PERSON><PERSON>, IdentityApi } from '@backstage/core-plugin-api';
import {
  ChoreoAuthApi,
  ChoreoAuthState,
  ChoreoTokens,
  ChoreoUserInfo,
  ChoreoOAuthConfig,
  ChoreoTokenStorage,
  OAuthState,
  TokenResponse,
  ChoreoAuthError,
  ChoreoAuthenticationError,
  ChoreoTokenExpiredError,
  ChoreoNetworkError,
} from './types';
import {
  generatePKCEChallenge,
  createOAuthState,
  validateOAuthState,
  buildAuthorizationUrl,
  parseAuthorizationResponse,
  extractUserInfoFromToken,
  isTokenExpired,
} from './utils';

/**
 * Default implementation of ChoreoAuthApi using OAuth 2.0 with PKCE
 */
export class DefaultChoreoAuthApi implements ChoreoAuthApi {
  private static readonly OAUTH_STATE_KEY = 'choreo-oauth-state';
  private static readonly POPUP_WINDOW_FEATURES = 'width=500,height=600,scrollbars=yes,resizable=yes';
  
  private readonly config: ChoreoOAuthConfig;
  private readonly tokenStorage: ChoreoTokenStorage;
  private readonly errorApi: ErrorApi;
  private readonly identityApi: IdentityApi;
  private readonly authStateSubject = new BehaviorSubject<ChoreoAuthState>({
    isAuthenticated: false,
    isLoading: false,
  });

  constructor(
    config: ChoreoOAuthConfig,
    tokenStorage: ChoreoTokenStorage,
    errorApi: ErrorApi,
    identityApi: IdentityApi,
  ) {
    this.config = config;
    this.tokenStorage = tokenStorage;
    this.errorApi = errorApi;
    this.identityApi = identityApi;
    
    // Initialize authentication state
    this.initializeAuthState();
  }

  private async initializeAuthState(): Promise<void> {
    try {
      this.updateAuthState({ isLoading: true });
      
      const tokens = await this.tokenStorage.getTokens();
      const isValid = await this.tokenStorage.areTokensValid();
      
      if (tokens && isValid) {
        const user = extractUserInfoFromToken(tokens.accessToken);
        this.updateAuthState({
          isAuthenticated: true,
          isLoading: false,
          user,
          tokens,
        });
      } else {
        // Clear invalid tokens
        if (tokens) {
          await this.tokenStorage.clearTokens();
        }
        this.updateAuthState({
          isAuthenticated: false,
          isLoading: false,
        });
      }
    } catch (error) {
      this.handleError(error, 'Failed to initialize authentication state');
      this.updateAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Unknown error'),
      });
    }
  }

  async getAuthState(): Promise<ChoreoAuthState> {
    return this.authStateSubject.value;
  }

  async authenticate(): Promise<ChoreoTokens> {
    try {
      this.updateAuthState({ isLoading: true });
      
      // Generate PKCE challenge
      const pkce = await generatePKCEChallenge();
      
      // Create OAuth state
      const oauthState = createOAuthState(pkce.codeVerifier, this.config.redirectUri);
      
      // Store OAuth state temporarily
      sessionStorage.setItem(DefaultChoreoAuthApi.OAUTH_STATE_KEY, JSON.stringify(oauthState));
      
      // Build authorization URL
      const authUrl = buildAuthorizationUrl(
        this.config.authorizationEndpoint,
        this.config.clientId,
        this.config.redirectUri,
        oauthState.state,
        pkce.codeChallenge,
        this.config.scopes,
        this.config.additionalParams,
      );
      
      // Open popup window for authentication
      const authCode = await this.openAuthPopup(authUrl);
      
      // Exchange authorization code for tokens
      const tokens = await this.exchangeCodeForTokens(authCode, oauthState.codeVerifier);
      
      // Store tokens securely
      await this.tokenStorage.storeTokens(tokens);
      
      // Extract user info and update state
      const user = extractUserInfoFromToken(tokens.accessToken);
      this.updateAuthState({
        isAuthenticated: true,
        isLoading: false,
        user,
        tokens,
      });
      
      return tokens;
    } catch (error) {
      this.handleError(error, 'Authentication failed');
      this.updateAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Authentication failed'),
      });
      throw error;
    } finally {
      // Clean up OAuth state
      sessionStorage.removeItem(DefaultChoreoAuthApi.OAUTH_STATE_KEY);
    }
  }

  async signOut(): Promise<void> {
    try {
      await this.tokenStorage.clearTokens();
      this.updateAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: undefined,
        tokens: undefined,
      });
    } catch (error) {
      this.handleError(error, 'Sign out failed');
      throw error;
    }
  }

  async getAccessToken(): Promise<string | null> {
    try {
      const tokens = await this.tokenStorage.getTokens();
      
      if (!tokens) {
        return null;
      }
      
      // Check if token is expired
      if (isTokenExpired(tokens.accessToken)) {
        // Try to refresh the token
        if (tokens.refreshToken) {
          try {
            const newTokens = await this.refreshToken();
            return newTokens.accessToken;
          } catch (error) {
            // Refresh failed, clear tokens and return null
            await this.tokenStorage.clearTokens();
            this.updateAuthState({
              isAuthenticated: false,
              isLoading: false,
              user: undefined,
              tokens: undefined,
            });
            return null;
          }
        } else {
          // No refresh token available, clear tokens
          await this.tokenStorage.clearTokens();
          this.updateAuthState({
            isAuthenticated: false,
            isLoading: false,
            user: undefined,
            tokens: undefined,
          });
          return null;
        }
      }
      
      return tokens.accessToken;
    } catch (error) {
      this.handleError(error, 'Failed to get access token');
      return null;
    }
  }

  async refreshToken(): Promise<ChoreoTokens> {
    const tokens = await this.tokenStorage.getTokens();
    
    if (!tokens?.refreshToken) {
      throw new ChoreoTokenExpiredError('No refresh token available');
    }
    
    try {
      const response = await fetch(this.config.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: tokens.refreshToken,
          client_id: this.config.clientId,
        }),
      });
      
      if (!response.ok) {
        throw new ChoreoAuthenticationError(`Token refresh failed: ${response.status} ${response.statusText}`);
      }
      
      const tokenResponse: TokenResponse = await response.json();
      
      const newTokens: ChoreoTokens = {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token || tokens.refreshToken,
        idToken: tokenResponse.id_token,
        expiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000),
        tokenType: tokenResponse.token_type,
        scope: tokenResponse.scope,
      };
      
      await this.tokenStorage.storeTokens(newTokens);
      
      // Update auth state with new tokens
      const user = extractUserInfoFromToken(newTokens.accessToken);
      this.updateAuthState({
        isAuthenticated: true,
        isLoading: false,
        user,
        tokens: newTokens,
      });
      
      return newTokens;
    } catch (error) {
      if (error instanceof ChoreoAuthError) {
        throw error;
      }
      throw new ChoreoNetworkError(`Token refresh failed: ${error}`);
    }
  }

  async isAuthenticated(): Promise<boolean> {
    return this.tokenStorage.areTokensValid();
  }

  async getUserInfo(): Promise<ChoreoUserInfo | null> {
    const tokens = await this.tokenStorage.getTokens();
    
    if (!tokens) {
      return null;
    }
    
    try {
      return extractUserInfoFromToken(tokens.accessToken);
    } catch (error) {
      this.handleError(error, 'Failed to extract user info from token');
      return null;
    }
  }

  authState$(): Observable<ChoreoAuthState> {
    return this.authStateSubject.asObservable();
  }

  private async openAuthPopup(authUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const popup = window.open(authUrl, 'choreo-auth', DefaultChoreoAuthApi.POPUP_WINDOW_FEATURES);
      
      if (!popup) {
        reject(new ChoreoAuthenticationError('Failed to open authentication popup. Please allow popups for this site.'));
        return;
      }
      
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new ChoreoAuthenticationError('Authentication popup was closed'));
        }
      }, 1000);
      
      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) {
          return;
        }
        
        if (event.data.type === 'CHOREO_AUTH_SUCCESS') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();
          resolve(event.data.code);
        } else if (event.data.type === 'CHOREO_AUTH_ERROR') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();
          reject(new ChoreoAuthenticationError(event.data.error || 'Authentication failed'));
        }
      };
      
      window.addEventListener('message', messageHandler);
    });
  }

  private async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<ChoreoTokens> {
    try {
      const response = await fetch(this.config.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code,
          redirect_uri: this.config.redirectUri,
          client_id: this.config.clientId,
          code_verifier: codeVerifier,
        }),
      });
      
      if (!response.ok) {
        throw new ChoreoAuthenticationError(`Token exchange failed: ${response.status} ${response.statusText}`);
      }
      
      const tokenResponse: TokenResponse = await response.json();
      
      return {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        expiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000),
        tokenType: tokenResponse.token_type,
        scope: tokenResponse.scope,
      };
    } catch (error) {
      if (error instanceof ChoreoAuthError) {
        throw error;
      }
      throw new ChoreoNetworkError(`Token exchange failed: ${error}`);
    }
  }

  private updateAuthState(updates: Partial<ChoreoAuthState>): void {
    const currentState = this.authStateSubject.value;
    this.authStateSubject.next({ ...currentState, ...updates });
  }

  private handleError(error: unknown, context: string): void {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    this.errorApi.post({
      message: `${context}: ${errorMessage}`,
      severity: 'error',
    });
  }
}
