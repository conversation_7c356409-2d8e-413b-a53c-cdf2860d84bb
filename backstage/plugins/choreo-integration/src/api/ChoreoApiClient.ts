import { 
  ChoreoApiClient, 
  ChoreoApiClientConfig, 
  ChoreoAuthApi,
  ChoreoNetworkError,
  ChoreoAuthenticationError 
} from './types';

/**
 * HTTP client for making authenticated requests to Choreo APIs
 */
export class DefaultChoreoApiClient implements ChoreoApiClient {
  private readonly baseUrl: string;
  private readonly authApi: ChoreoAuthApi;

  constructor(config: ChoreoApiClientConfig) {
    this.baseUrl = config.baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.authApi = config.authApi;
  }

  async get<T>(path: string, options: RequestInit = {}): Promise<T> {
    return this.request<T>('GET', path, undefined, options);
  }

  async post<T>(path: string, data?: any, options: RequestInit = {}): Promise<T> {
    return this.request<T>('POST', path, data, options);
  }

  async put<T>(path: string, data?: any, options: RequestInit = {}): Promise<T> {
    return this.request<T>('PUT', path, data, options);
  }

  async delete<T>(path: string, options: RequestInit = {}): Promise<T> {
    return this.request<T>('DELETE', path, undefined, options);
  }

  async patch<T>(path: string, data?: any, options: RequestInit = {}): Promise<T> {
    return this.request<T>('PATCH', path, data, options);
  }

  private async request<T>(
    method: string,
    path: string,
    data?: any,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${path.startsWith('/') ? path : `/${path}`}`;
    
    // Get access token
    const accessToken = await this.authApi.getAccessToken();
    if (!accessToken) {
      throw new ChoreoAuthenticationError('No valid access token available. Please authenticate first.');
    }

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
      ...options.headers,
    };

    // Prepare request body
    let body: string | undefined;
    if (data !== undefined && method !== 'GET' && method !== 'DELETE') {
      if (typeof data === 'string') {
        body = data;
      } else if (data instanceof FormData) {
        body = data as any;
        // Remove Content-Type header for FormData (browser will set it with boundary)
        delete (headers as any)['Content-Type'];
      } else {
        body = JSON.stringify(data);
      }
    }

    try {
      const response = await fetch(url, {
        method,
        headers,
        body,
        ...options,
      });

      // Handle authentication errors
      if (response.status === 401) {
        throw new ChoreoAuthenticationError('Authentication failed. Please sign in again.');
      }

      // Handle other HTTP errors
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorBody = await response.text();
          if (errorBody) {
            // Try to parse as JSON for structured error
            try {
              const errorJson = JSON.parse(errorBody);
              errorMessage = errorJson.message || errorJson.error || errorMessage;
            } catch {
              // Use raw text if not JSON
              errorMessage = errorBody;
            }
          }
        } catch {
          // Ignore errors when reading response body
        }
        
        throw new ChoreoNetworkError(`Request failed: ${errorMessage}`);
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type');
      if (!contentType || response.status === 204) {
        return {} as T;
      }

      // Parse response based on content type
      if (contentType.includes('application/json')) {
        return await response.json();
      } else if (contentType.includes('text/')) {
        return await response.text() as unknown as T;
      } else {
        // For other content types, return as blob
        return await response.blob() as unknown as T;
      }
    } catch (error) {
      if (error instanceof ChoreoAuthenticationError || error instanceof ChoreoNetworkError) {
        throw error;
      }
      
      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ChoreoNetworkError('Network error: Please check your internet connection.');
      }
      
      throw new ChoreoNetworkError(`Request failed: ${error}`);
    }
  }
}
