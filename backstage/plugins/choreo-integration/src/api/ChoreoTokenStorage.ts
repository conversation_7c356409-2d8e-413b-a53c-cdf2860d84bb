import { <PERSON>orage<PERSON><PERSON> } from '@backstage/core-plugin-api';
import { ChoreoTokenStorage, ChoreoTokens } from './types';
import { encryptData, decryptData, isTokenExpired } from './utils';

/**
 * Secure token storage implementation using Backstage StorageApi
 */
export class DefaultChoreoTokenStorage implements ChoreoTokenStorage {
  private static readonly STORAGE_KEY = 'choreo-auth-tokens';
  private static readonly ENCRYPTION_KEY = 'choreo-plugin-encryption-key';
  
  private readonly storageApi: StorageApi;

  constructor(storageApi: StorageApi) {
    this.storageApi = storageApi.forBucket('choreo-integration');
  }

  async storeTokens(tokens: ChoreoTokens): Promise<void> {
    try {
      const tokenData = JSON.stringify({
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        idToken: tokens.idToken,
        expiresAt: tokens.expiresAt.toISOString(),
        tokenType: tokens.tokenType,
        scope: tokens.scope,
      });

      // Encrypt the token data before storing
      const encryptedData = await encryptData(tokenData, DefaultChoreoTokenStorage.ENCRYPTION_KEY);
      
      await this.storageApi.set(DefaultChoreoTokenStorage.STORAGE_KEY, encryptedData);
    } catch (error) {
      throw new Error(`Failed to store Choreo tokens: ${error}`);
    }
  }

  async getTokens(): Promise<ChoreoTokens | null> {
    try {
      const encryptedData = this.storageApi.snapshot(DefaultChoreoTokenStorage.STORAGE_KEY).value;
      
      if (!encryptedData || typeof encryptedData !== 'string') {
        return null;
      }

      // Decrypt the token data
      const tokenData = await decryptData(encryptedData, DefaultChoreoTokenStorage.ENCRYPTION_KEY);
      const parsed = JSON.parse(tokenData);

      return {
        accessToken: parsed.accessToken,
        refreshToken: parsed.refreshToken,
        idToken: parsed.idToken,
        expiresAt: new Date(parsed.expiresAt),
        tokenType: parsed.tokenType,
        scope: parsed.scope,
      };
    } catch (error) {
      // If decryption or parsing fails, clear the corrupted data
      await this.clearTokens();
      return null;
    }
  }

  async clearTokens(): Promise<void> {
    try {
      await this.storageApi.remove(DefaultChoreoTokenStorage.STORAGE_KEY);
    } catch (error) {
      throw new Error(`Failed to clear Choreo tokens: ${error}`);
    }
  }

  async areTokensValid(): Promise<boolean> {
    try {
      const tokens = await this.getTokens();
      
      if (!tokens) {
        return false;
      }

      // Check if access token is expired
      if (isTokenExpired(tokens.accessToken)) {
        return false;
      }

      // Check if the stored expiry time has passed
      const now = new Date();
      if (now >= tokens.expiresAt) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }
}
