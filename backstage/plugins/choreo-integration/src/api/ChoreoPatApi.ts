import { createApiRef } from '@backstage/core-plugin-api';

/**
 * Simple Choreo API client using Personal Access Token (PAT)
 */
export interface ChoreoPatApi {
  /**
   * Make a GET request to Choreo API
   */
  get<T = any>(path: string): Promise<T>;
  
  /**
   * Make a POST request to Choreo API
   */
  post<T = any>(path: string, data?: any): Promise<T>;
  
  /**
   * Make a PUT request to Choreo API
   */
  put<T = any>(path: string, data?: any): Promise<T>;
  
  /**
   * Make a DELETE request to Choreo API
   */
  delete<T = any>(path: string): Promise<T>;
  
  /**
   * Check if PAT is configured
   */
  isConfigured(): boolean;
}

export const choreoPatApiRef = createApiRef<ChoreoPatApi>({
  id: 'plugin.choreo-integration.pat-api',
});

/**
 * Default implementation of Choreo PAT API
 */
export class DefaultChoreoPatApi implements ChoreoPatApi {
  private readonly baseUrl: string;
  private readonly pat: string;

  constructor(baseUrl: string = 'https://api.choreo.dev/v1', pat?: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.pat = pat || process.env.REACT_APP_CHOREO_PAT || '';
  }

  isConfigured(): boolean {
    return !!this.pat;
  }

  private async makeRequest<T>(
    method: string,
    path: string,
    data?: any,
  ): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error(
        'Choreo PAT not configured. Please set REACT_APP_CHOREO_PAT environment variable.',
      );
    }

    const url = `${this.baseUrl}${path.startsWith('/') ? path : `/${path}`}`;
    
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.pat}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    const config: RequestInit = {
      method,
      headers,
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Choreo API request failed: ${response.status} ${response.statusText}. ${errorText}`,
        );
      }

      // Handle empty responses
      if (response.status === 204 || response.headers.get('content-length') === '0') {
        return {} as T;
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }

      return (await response.text()) as unknown as T;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to call Choreo API: ${error.message}`);
      }
      throw new Error('Unknown error occurred while calling Choreo API');
    }
  }

  async get<T = any>(path: string): Promise<T> {
    return this.makeRequest<T>('GET', path);
  }

  async post<T = any>(path: string, data?: any): Promise<T> {
    return this.makeRequest<T>('POST', path, data);
  }

  async put<T = any>(path: string, data?: any): Promise<T> {
    return this.makeRequest<T>('PUT', path, data);
  }

  async delete<T = any>(path: string): Promise<T> {
    return this.makeRequest<T>('DELETE', path);
  }
}
