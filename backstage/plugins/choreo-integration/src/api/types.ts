/**
 * Types and interfaces for Choreo authentication integration
 */

export interface ChoreoTokens {
  accessToken: string;
  refreshToken?: string;
  idToken?: string;
  expiresAt: Date;
  tokenType: string;
  scope: string;
}

export interface ChoreoUserInfo {
  sub: string;
  email: string;
  name: string;
  given_name: string;
  family_name: string;
  organization: {
    handle: string;
    uuid: string;
  };
  organizations: string[];
}

export interface ChoreoAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user?: ChoreoUserInfo;
  tokens?: ChoreoTokens;
  error?: Error;
}

export interface ChoreoOAuthConfig {
  authorizationEndpoint: string;
  tokenEndpoint: string;
  clientId: string;
  redirectUri: string;
  scopes: string[];
  additionalParams?: Record<string, string>;
}

export interface ChoreoAuthApi {
  /**
   * Get current authentication state
   */
  getAuthState(): Promise<ChoreoAuthState>;

  /**
   * Initiate OAuth 2.0 authentication flow
   */
  authenticate(): Promise<ChoreoTokens>;

  /**
   * Sign out and clear stored tokens
   */
  signOut(): Promise<void>;

  /**
   * Get valid access token (refreshes if needed)
   */
  getAccessToken(): Promise<string | null>;

  /**
   * Refresh access token using refresh token
   */
  refreshToken(): Promise<ChoreoTokens>;

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): Promise<boolean>;

  /**
   * Get user information from stored tokens
   */
  getUserInfo(): Promise<ChoreoUserInfo | null>;

  /**
   * Observable for authentication state changes
   */
  authState$(): import('@backstage/types').Observable<ChoreoAuthState>;
}

export interface ChoreoTokenStorage {
  /**
   * Store tokens securely
   */
  storeTokens(tokens: ChoreoTokens): Promise<void>;

  /**
   * Retrieve stored tokens
   */
  getTokens(): Promise<ChoreoTokens | null>;

  /**
   * Clear stored tokens
   */
  clearTokens(): Promise<void>;

  /**
   * Check if tokens are valid (not expired)
   */
  areTokensValid(): Promise<boolean>;
}

export interface PKCEChallenge {
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: string;
}

export interface OAuthState {
  state: string;
  codeVerifier: string;
  redirectUri: string;
  timestamp: number;
}

export interface AuthorizationResponse {
  code: string;
  state: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  id_token?: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface ChoreoApiClientConfig {
  baseUrl: string;
  authApi: ChoreoAuthApi;
}

export interface ChoreoApiClient {
  /**
   * Make authenticated GET request
   */
  get<T>(path: string, options?: RequestInit): Promise<T>;

  /**
   * Make authenticated POST request
   */
  post<T>(path: string, data?: any, options?: RequestInit): Promise<T>;

  /**
   * Make authenticated PUT request
   */
  put<T>(path: string, data?: any, options?: RequestInit): Promise<T>;

  /**
   * Make authenticated DELETE request
   */
  delete<T>(path: string, options?: RequestInit): Promise<T>;

  /**
   * Make authenticated PATCH request
   */
  patch<T>(path: string, data?: any, options?: RequestInit): Promise<T>;
}

export class ChoreoAuthError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly originalError?: Error,
  ) {
    super(message);
    this.name = 'ChoreoAuthError';
  }
}

export class ChoreoTokenExpiredError extends ChoreoAuthError {
  constructor(message = 'Choreo access token has expired') {
    super(message, 'TOKEN_EXPIRED');
    this.name = 'ChoreoTokenExpiredError';
  }
}

export class ChoreoAuthenticationError extends ChoreoAuthError {
  constructor(message = 'Choreo authentication failed') {
    super(message, 'AUTHENTICATION_FAILED');
    this.name = 'ChoreoAuthenticationError';
  }
}

export class ChoreoNetworkError extends ChoreoAuthError {
  constructor(message = 'Network error during Choreo authentication') {
    super(message, 'NETWORK_ERROR');
    this.name = 'ChoreoNetworkError';
  }
}
