import { DefaultChoreoAuthApi } from './DefaultChoreoAuthApi';
import { DefaultChoreoTokenStorage } from './ChoreoTokenStorage';
import { ChoreoOAuthConfig } from './types';

// Mock dependencies
const mockStorageApi = {
  forBucket: jest.fn(() => ({
    set: jest.fn(),
    snapshot: jest.fn(() => ({ value: null })),
    remove: jest.fn(),
  })),
};

const mockErrorApi = {
  post: jest.fn(),
};

const mockIdentityApi = {
  getCredentials: jest.fn(),
  getProfileInfo: jest.fn(),
  getBackstageIdentity: jest.fn(),
  signOut: jest.fn(),
};

const mockConfig: ChoreoOAuthConfig = {
  authorizationEndpoint: 'https://console.choreo.dev/login',
  tokenEndpoint: 'https://sts.choreo.dev/oauth2/token',
  clientId: 'test-client-id',
  redirectUri: 'http://localhost:3000/choreo-integration/auth/callback',
  scopes: ['openid', 'profile', 'email'],
};

describe('DefaultChoreoAuthApi', () => {
  let authApi: DefaultChoreoAuthApi;
  let tokenStorage: DefaultChoreoTokenStorage;

  beforeEach(() => {
    tokenStorage = new DefaultChoreoTokenStorage(mockStorageApi as any);
    authApi = new DefaultChoreoAuthApi(
      mockConfig,
      tokenStorage,
      mockErrorApi as any,
      mockIdentityApi as any,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAuthState', () => {
    it('should return initial auth state', async () => {
      const authState = await authApi.getAuthState();
      
      expect(authState).toEqual({
        isAuthenticated: false,
        isLoading: false,
      });
    });
  });

  describe('isAuthenticated', () => {
    it('should return false when no tokens are stored', async () => {
      jest.spyOn(tokenStorage, 'areTokensValid').mockResolvedValue(false);
      
      const isAuth = await authApi.isAuthenticated();
      
      expect(isAuth).toBe(false);
    });

    it('should return true when valid tokens are stored', async () => {
      jest.spyOn(tokenStorage, 'areTokensValid').mockResolvedValue(true);
      
      const isAuth = await authApi.isAuthenticated();
      
      expect(isAuth).toBe(true);
    });
  });

  describe('signOut', () => {
    it('should clear tokens and update auth state', async () => {
      const clearTokensSpy = jest.spyOn(tokenStorage, 'clearTokens').mockResolvedValue();
      
      await authApi.signOut();
      
      expect(clearTokensSpy).toHaveBeenCalled();
      
      const authState = await authApi.getAuthState();
      expect(authState.isAuthenticated).toBe(false);
      expect(authState.user).toBeUndefined();
      expect(authState.tokens).toBeUndefined();
    });
  });

  describe('getAccessToken', () => {
    it('should return null when no tokens are stored', async () => {
      jest.spyOn(tokenStorage, 'getTokens').mockResolvedValue(null);
      
      const token = await authApi.getAccessToken();
      
      expect(token).toBeNull();
    });

    it('should return access token when valid tokens are stored', async () => {
      const mockTokens = {
        accessToken: 'valid-access-token',
        refreshToken: 'refresh-token',
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
        tokenType: 'Bearer',
        scope: 'openid profile email',
      };
      
      jest.spyOn(tokenStorage, 'getTokens').mockResolvedValue(mockTokens);
      
      const token = await authApi.getAccessToken();
      
      expect(token).toBe('valid-access-token');
    });
  });

  describe('getUserInfo', () => {
    it('should return null when no tokens are stored', async () => {
      jest.spyOn(tokenStorage, 'getTokens').mockResolvedValue(null);
      
      const userInfo = await authApi.getUserInfo();
      
      expect(userInfo).toBeNull();
    });

    it('should extract user info from valid JWT token', async () => {
      // Mock JWT token with user info (simplified for testing)
      const mockJwtPayload = {
        sub: 'user123',
        idp_claims: {
          email: '<EMAIL>',
          name: 'Test User',
          given_name: 'Test',
          family_name: 'User',
        },
        organization: {
          handle: 'test-org',
          uuid: 'org-uuid',
        },
        organizations: ['test-org'],
      };
      
      // Create a mock JWT token (header.payload.signature)
      const mockJwtToken = `header.${btoa(JSON.stringify(mockJwtPayload))}.signature`;
      
      const mockTokens = {
        accessToken: mockJwtToken,
        refreshToken: 'refresh-token',
        expiresAt: new Date(Date.now() + 3600000),
        tokenType: 'Bearer',
        scope: 'openid profile email',
      };
      
      jest.spyOn(tokenStorage, 'getTokens').mockResolvedValue(mockTokens);
      
      const userInfo = await authApi.getUserInfo();
      
      expect(userInfo).toEqual({
        sub: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        given_name: 'Test',
        family_name: 'User',
        organization: {
          handle: 'test-org',
          uuid: 'org-uuid',
        },
        organizations: ['test-org'],
      });
    });
  });
});
