import { PKCEChallenge, OAuthState, ChoreoUserInfo } from './types';

/**
 * Generate a cryptographically secure random string
 */
export function generateRandomString(length: number): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => charset[byte % charset.length]).join('');
}

/**
 * Generate PKCE challenge for OAuth 2.0 authorization code flow
 */
export async function generatePKCEChallenge(): Promise<PKCEChallenge> {
  const codeVerifier = generateRandomString(128);
  
  // Create SHA256 hash of the code verifier
  const encoder = new TextEncoder();
  const data = encoder.encode(codeVerifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  
  // Convert to base64url encoding
  const codeChallenge = btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return {
    codeVerifier,
    codeChallenge,
    codeChallengeMethod: 'S256',
  };
}

/**
 * Generate OAuth state parameter
 */
export function generateOAuthState(): string {
  return generateRandomString(32);
}

/**
 * Create OAuth state object for storage
 */
export function createOAuthState(codeVerifier: string, redirectUri: string): OAuthState {
  return {
    state: generateOAuthState(),
    codeVerifier,
    redirectUri,
    timestamp: Date.now(),
  };
}

/**
 * Validate OAuth state (check expiry and format)
 */
export function validateOAuthState(state: OAuthState | null): boolean {
  if (!state) return false;
  
  // Check if state is expired (30 minutes)
  const maxAge = 30 * 60 * 1000; // 30 minutes in milliseconds
  const isExpired = Date.now() - state.timestamp > maxAge;
  
  return !isExpired && 
         typeof state.state === 'string' && 
         typeof state.codeVerifier === 'string' &&
         typeof state.redirectUri === 'string';
}

/**
 * Parse JWT token payload without verification (for user info extraction)
 * Note: This is only for extracting user info, not for security validation
 */
export function parseJWTPayload(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const payload = parts[1];
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    
    return JSON.parse(decoded);
  } catch (error) {
    throw new Error(`Failed to parse JWT payload: ${error}`);
  }
}

/**
 * Extract user information from Choreo JWT token
 */
export function extractUserInfoFromToken(accessToken: string): ChoreoUserInfo {
  const payload = parseJWTPayload(accessToken);
  
  return {
    sub: payload.sub,
    email: payload.idp_claims?.email || '',
    name: payload.idp_claims?.name || '',
    given_name: payload.idp_claims?.given_name || '',
    family_name: payload.idp_claims?.family_name || '',
    organization: payload.organization || { handle: '', uuid: '' },
    organizations: payload.organizations || [],
  };
}

/**
 * Check if JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = parseJWTPayload(token);
    const exp = payload.exp;
    
    if (!exp) return true;
    
    // Add 30 second buffer to account for clock skew
    const expiryTime = exp * 1000;
    const currentTime = Date.now() + 30000;
    
    return currentTime >= expiryTime;
  } catch (error) {
    // If we can't parse the token, consider it expired
    return true;
  }
}

/**
 * Build OAuth authorization URL
 */
export function buildAuthorizationUrl(
  authorizationEndpoint: string,
  clientId: string,
  redirectUri: string,
  state: string,
  codeChallenge: string,
  scopes: string[],
  additionalParams: Record<string, string> = {},
): string {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: clientId,
    redirect_uri: redirectUri,
    state,
    code_challenge: codeChallenge,
    code_challenge_method: 'S256',
    scope: scopes.join(' '),
    ...additionalParams,
  });

  return `${authorizationEndpoint}?${params.toString()}`;
}

/**
 * Parse authorization response from URL
 */
export function parseAuthorizationResponse(url: string): { code?: string; state?: string; error?: string; error_description?: string } {
  const urlObj = new URL(url);
  const params = urlObj.searchParams;
  
  return {
    code: params.get('code') || undefined,
    state: params.get('state') || undefined,
    error: params.get('error') || undefined,
    error_description: params.get('error_description') || undefined,
  };
}

/**
 * Simple encryption for token storage (using Web Crypto API)
 */
export async function encryptData(data: string, key: string): Promise<string> {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key.padEnd(32, '0').slice(0, 32)); // Ensure 32 bytes
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'AES-GCM' },
    false,
    ['encrypt']
  );
  
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const encodedData = encoder.encode(data);
  
  const encrypted = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    cryptoKey,
    encodedData
  );
  
  // Combine IV and encrypted data
  const combined = new Uint8Array(iv.length + encrypted.byteLength);
  combined.set(iv);
  combined.set(new Uint8Array(encrypted), iv.length);
  
  return btoa(String.fromCharCode(...combined));
}

/**
 * Simple decryption for token storage
 */
export async function decryptData(encryptedData: string, key: string): Promise<string> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  const keyData = encoder.encode(key.padEnd(32, '0').slice(0, 32)); // Ensure 32 bytes
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'AES-GCM' },
    false,
    ['decrypt']
  );
  
  const combined = new Uint8Array(
    atob(encryptedData).split('').map(char => char.charCodeAt(0))
  );
  
  const iv = combined.slice(0, 12);
  const encrypted = combined.slice(12);
  
  const decrypted = await crypto.subtle.decrypt(
    { name: 'AES-GCM', iv },
    cryptoKey,
    encrypted
  );
  
  return decoder.decode(decrypted);
}
