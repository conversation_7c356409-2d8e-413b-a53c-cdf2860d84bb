import { ReactNode } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CircularProgress,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import LockIcon from '@material-ui/icons/Lock';
import { useChoreoAuth } from '../ChoreoAuthProvider/ChoreoAuthProvider';
import { ChoreoAuthButton } from '../ChoreoAuthButton/ChoreoAuthButton';

const useStyles = makeStyles(theme => ({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '400px',
    padding: theme.spacing(2),
  },
  card: {
    maxWidth: 500,
    textAlign: 'center',
  },
  icon: {
    fontSize: 64,
    color: theme.palette.text.secondary,
    marginBottom: theme.spacing(2),
  },
  loadingContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: theme.spacing(2),
  },
}));

interface ChoreoAuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  showUserInfo?: boolean;
}

/**
 * Authentication guard component that protects content behind Choreo authentication
 * Shows login UI when user is not authenticated
 */
export const ChoreoAuthGuard: React.FC<ChoreoAuthGuardProps> = ({
  children,
  fallback,
  showUserInfo = false,
}) => {
  const classes = useStyles();
  const { authState } = useChoreoAuth();

  // Show loading state
  if (authState.isLoading) {
    return (
      <Box className={classes.container}>
        <div className={classes.loadingContainer}>
          <CircularProgress size={48} />
          <Typography variant="h6" color="textSecondary">
            Checking authentication...
          </Typography>
        </div>
      </Box>
    );
  }

  // Show error state
  if (authState.error) {
    return (
      <Box className={classes.container}>
        <Card className={classes.card}>
          <CardContent>
            <Alert severity="error" style={{ marginBottom: 16 }}>
              Authentication Error: {authState.error.message}
            </Alert>
            <ChoreoAuthButton 
              variant="contained" 
              fullWidth 
              showUserInfo={showUserInfo}
            />
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Show authentication required state
  if (!authState.isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <Box className={classes.container}>
        <Card className={classes.card}>
          <CardContent>
            <LockIcon className={classes.icon} />
            <Typography variant="h5" component="h2" gutterBottom>
              Authentication Required
            </Typography>
            <Typography variant="body1" color="textSecondary" paragraph>
              You need to sign in to Choreo to access this content. 
              Please authenticate to continue.
            </Typography>
            <ChoreoAuthButton 
              variant="contained" 
              size="large" 
              fullWidth 
              showUserInfo={showUserInfo}
            />
          </CardContent>
        </Card>
      </Box>
    );
  }

  // User is authenticated, show protected content
  return <>{children}</>;
};
