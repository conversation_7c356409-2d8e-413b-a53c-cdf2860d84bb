import { useEffect } from 'react';
import { parseAuthorizationResponse } from '../../api/utils';

/**
 * OAuth callback component that handles the authorization response
 * This component is displayed in the popup window during authentication
 */
export const ChoreoAuthCallback = () => {
  useEffect(() => {
    const handleAuthCallback = () => {
      try {
        const currentUrl = window.location.href;
        const response = parseAuthorizationResponse(currentUrl);
        
        if (response.error) {
          // Send error to parent window
          window.opener?.postMessage({
            type: 'CHOREO_AUTH_ERROR',
            error: response.error_description || response.error,
          }, window.location.origin);
        } else if (response.code && response.state) {
          // Validate state parameter (basic check)
          const storedState = sessionStorage.getItem('choreo-oauth-state');
          if (storedState) {
            const oauthState = JSON.parse(storedState);
            if (oauthState.state === response.state) {
              // Send success to parent window
              window.opener?.postMessage({
                type: 'CHOREO_AUTH_SUCCESS',
                code: response.code,
                state: response.state,
              }, window.location.origin);
            } else {
              // State mismatch - possible CSRF attack
              window.opener?.postMessage({
                type: 'CHOREO_AUTH_ERROR',
                error: 'Invalid state parameter',
              }, window.location.origin);
            }
          } else {
            window.opener?.postMessage({
              type: 'CHOREO_AUTH_ERROR',
              error: 'Missing OAuth state',
            }, window.location.origin);
          }
        } else {
          window.opener?.postMessage({
            type: 'CHOREO_AUTH_ERROR',
            error: 'Invalid authorization response',
          }, window.location.origin);
        }
      } catch (error) {
        window.opener?.postMessage({
          type: 'CHOREO_AUTH_ERROR',
          error: `Callback processing failed: ${error}`,
        }, window.location.origin);
      }
      
      // Close the popup window
      window.close();
    };
    
    // Handle the callback immediately when component mounts
    handleAuthCallback();
  }, []);

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{ textAlign: 'center' }}>
        <h2>Processing Authentication...</h2>
        <p>Please wait while we complete your authentication.</p>
        <p>This window will close automatically.</p>
      </div>
    </div>
  );
};
