import { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { useApi } from '@backstage/core-plugin-api';
import { choreoAuthApiRef } from '../../api/ChoreoAuthApi';
import { ChoreoAuthState } from '../../api/types';

interface ChoreoAuthContextValue {
  authState: ChoreoAuthState;
  refreshAuthState: () => Promise<void>;
}

const ChoreoAuthContext = createContext<ChoreoAuthContextValue | undefined>(undefined);

interface ChoreoAuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication provider that manages Choreo authentication state
 * and provides it to child components via React context
 */
export const ChoreoAuthProvider: React.FC<ChoreoAuthProviderProps> = ({ children }) => {
  const choreoAuthApi = useApi(choreoAuthApiRef);
  const [authState, setAuthState] = useState<ChoreoAuthState>({
    isAuthenticated: false,
    isLoading: true,
  });

  const refreshAuthState = useCallback(async () => {
    try {
      const currentState = await choreoAuthApi.getAuthState();
      setAuthState(currentState);
    } catch (error) {
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Failed to get auth state'),
      });
    }
  }, [choreoAuthApi]);

  useEffect(() => {
    // Subscribe to auth state changes
    const subscription = choreoAuthApi.authState$().subscribe(newState => {
      setAuthState(newState);
    });

    // Initial state load
    refreshAuthState();

    return () => {
      subscription.unsubscribe();
    };
  }, [choreoAuthApi, refreshAuthState]);

  const contextValue: ChoreoAuthContextValue = {
    authState,
    refreshAuthState,
  };

  return (
    <ChoreoAuthContext.Provider value={contextValue}>
      {children}
    </ChoreoAuthContext.Provider>
  );
};

/**
 * Hook to access Choreo authentication state and methods
 */
export const useChoreoAuth = (): ChoreoAuthContextValue => {
  const context = useContext(ChoreoAuthContext);
  if (!context) {
    throw new Error('useChoreoAuth must be used within a ChoreoAuthProvider');
  }
  return context;
};
