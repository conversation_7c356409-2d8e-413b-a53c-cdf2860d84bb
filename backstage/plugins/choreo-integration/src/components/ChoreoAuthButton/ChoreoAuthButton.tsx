import React, { useState } from 'react';
import { Button, CircularProgress, Tooltip } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import LockOpenIcon from '@material-ui/icons/LockOpen';
import { useApi } from '@backstage/core-plugin-api';
import { choreoAuthApiRef } from '../../api/ChoreoAuthApi';
import { useChoreoAuth } from '../ChoreoAuthProvider/ChoreoAuthProvider';

const useStyles = makeStyles(theme => ({
  button: {
    margin: theme.spacing(1),
  },
  progress: {
    marginRight: theme.spacing(1),
  },
  error: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
}));

interface ChoreoAuthButtonProps {
  variant?: 'text' | 'outlined' | 'contained';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  showUserInfo?: boolean;
}

/**
 * Authentication button component for Choreo integration
 * Handles both login and logout functionality
 */
export const ChoreoAuthButton: React.FC<ChoreoAuthButtonProps> = ({
  variant = 'contained',
  size = 'medium',
  fullWidth = false,
  showUserInfo = false,
}) => {
  const classes = useStyles();
  const choreoAuthApi = useApi(choreoAuthApiRef);
  const { authState } = useChoreoAuth();
  const [localError, setLocalError] = useState<string | null>(null);

  const handleLogin = async () => {
    try {
      setLocalError(null);
      await choreoAuthApi.authenticate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      setLocalError(errorMessage);
    }
  };

  const handleLogout = async () => {
    try {
      setLocalError(null);
      await choreoAuthApi.signOut();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
      setLocalError(errorMessage);
    }
  };

  const renderButton = () => {
    if (authState.isLoading) {
      return (
        <Button
          variant={variant}
          size={size}
          fullWidth={fullWidth}
          disabled
          className={classes.button}
          startIcon={<CircularProgress size={16} className={classes.progress} />}
        >
          {authState.isAuthenticated ? 'Signing out...' : 'Signing in...'}
        </Button>
      );
    }

    if (authState.isAuthenticated) {
      const buttonText = showUserInfo && authState.user 
        ? `Sign out (${authState.user.email})` 
        : 'Sign out of Choreo';

      return (
        <Tooltip title="Sign out of Choreo">
          <Button
            variant={variant}
            size={size}
            fullWidth={fullWidth}
            onClick={handleLogout}
            className={classes.button}
            startIcon={<ExitToAppIcon />}
            color="secondary"
          >
            {buttonText}
          </Button>
        </Tooltip>
      );
    }

    return (
      <Tooltip title="Sign in to Choreo to access your projects and APIs">
        <Button
          variant={variant}
          size={size}
          fullWidth={fullWidth}
          onClick={handleLogin}
          className={classes.button}
          startIcon={<LockOpenIcon />}
          color="primary"
        >
          Sign in to Choreo
        </Button>
      </Tooltip>
    );
  };

  const error = localError || authState.error?.message;

  return (
    <>
      {renderButton()}
      {error && (
        <Alert severity="error" className={classes.error}>
          {error}
        </Alert>
      )}
    </>
  );
};
