import React from 'react';
import {
  <PERSON>,
  Header,
  Content,
  ContentHeader,
  SupportButton,
} from '@backstage/core-components';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { ChoreoAuthProvider } from '../ChoreoAuthProvider';
import { ChoreoAuthGuard } from '../ChoreoAuthGuard';
import { ChoreoAuthButton } from '../ChoreoAuthButton';
import { ChoreoUserInfo } from '../ChoreoUserInfo';

const useStyles = makeStyles(theme => ({
  authCard: {
    marginBottom: theme.spacing(3),
  },
  welcomeCard: {
    marginBottom: theme.spacing(3),
  },
  featureCard: {
    height: '100%',
  },
}));

/**
 * Main Choreo integration page component
 */
export const ChoreoIntegrationPage = () => {
  const classes = useStyles();

  return (
    <ChoreoAuthProvider>
      <Page themeId="tool">
        <Header title="Choreo Integration" subtitle="Connect and manage your Choreo projects">
          <SupportButton>
            Integrate your Backstage instance with Choreo to manage applications, 
            APIs, and deployments from a single interface.
          </SupportButton>
        </Header>
        <Content>
          <ContentHeader title="Choreo Dashboard">
            <ChoreoAuthButton variant="outlined" showUserInfo />
          </ContentHeader>

          <Grid container spacing={3}>
            {/* Authentication Status Card */}
            <Grid item xs={12} md={6}>
              <Card className={classes.authCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Authentication Status
                  </Typography>
                  <ChoreoUserInfo />
                </CardContent>
              </Card>
            </Grid>

            {/* Welcome Card */}
            <Grid item xs={12} md={6}>
              <Card className={classes.welcomeCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Welcome to Choreo Integration
                  </Typography>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    This plugin provides seamless integration between Backstage and Choreo, 
                    allowing you to manage your cloud-native applications and APIs from within 
                    your developer portal.
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Sign in to Choreo to get started and access your projects, APIs, and deployment information.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Protected Content */}
            <Grid item xs={12}>
              <ChoreoAuthGuard>
                <Grid container spacing={3}>
                  {/* Projects Card */}
                  <Grid item xs={12} md={4}>
                    <Card className={classes.featureCard}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Projects
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          View and manage your Choreo projects. Access project details, 
                          components, and deployment information.
                        </Typography>
                        <Box mt={2}>
                          <Typography variant="caption" color="primary">
                            Coming soon...
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* APIs Card */}
                  <Grid item xs={12} md={4}>
                    <Card className={classes.featureCard}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          APIs
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Discover and manage your APIs. View API documentation, 
                          test endpoints, and monitor usage.
                        </Typography>
                        <Box mt={2}>
                          <Typography variant="caption" color="primary">
                            Coming soon...
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Deployments Card */}
                  <Grid item xs={12} md={4}>
                    <Card className={classes.featureCard}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Deployments
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Monitor your deployments across different environments. 
                          View deployment status and logs.
                        </Typography>
                        <Box mt={2}>
                          <Typography variant="caption" color="primary">
                            Coming soon...
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </ChoreoAuthGuard>
            </Grid>
          </Grid>
        </Content>
      </Page>
    </ChoreoAuthProvider>
  );
};
