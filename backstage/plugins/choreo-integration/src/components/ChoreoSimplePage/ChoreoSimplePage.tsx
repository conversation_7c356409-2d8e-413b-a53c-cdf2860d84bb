import { useState, useEffect, useCallback } from 'react';
import {
  Page,
  Header,
  Content,
  ContentHeader,
  SupportButton,
  Progress,
  ErrorPanel,
} from '@backstage/core-components';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Button,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { useApi } from '@backstage/core-plugin-api';
import { choreoPatApiRef } from '../../api/ChoreoPatApi';
import RefreshIcon from '@material-ui/icons/Refresh';
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import ErrorIcon from '@material-ui/icons/Error';

const useStyles = makeStyles(theme => ({
  statusCard: {
    marginBottom: theme.spacing(3),
  },
  successChip: {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
  },
  errorChip: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
  },
  refreshButton: {
    marginLeft: theme.spacing(1),
  },
}));

interface ChoreoProject {
  id: string;
  name: string;
  description?: string;
  status?: string;
}

/**
 * Simple Choreo integration page using PAT authentication
 */
export const ChoreoSimplePage = () => {
  const classes = useStyles();
  const choreoApi = useApi(choreoPatApiRef);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [projects, setProjects] = useState<ChoreoProject[]>([]);
  const [isConfigured, setIsConfigured] = useState(false);

  const fetchProjects = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Example API call - adjust the endpoint based on actual Choreo API
      const data = await choreoApi.get('/projects');
      setProjects(Array.isArray(data) ? data : data.projects || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch projects');
    } finally {
      setLoading(false);
    }
  }, [choreoApi]);

  useEffect(() => {
    setIsConfigured(choreoApi.isConfigured());
    if (choreoApi.isConfigured()) {
      fetchProjects();
    }
  }, [choreoApi, fetchProjects]);

  const handleRefresh = () => {
    if (isConfigured) {
      fetchProjects();
    }
  };

  if (!isConfigured) {
    return (
      <Page themeId="tool">
        <Header title="Choreo Integration" subtitle="Connect to your Choreo projects">
          <SupportButton>
            Simple Choreo integration using Personal Access Token authentication.
          </SupportButton>
        </Header>
        <Content>
          <ErrorPanel 
            title="Configuration Required"
            error={new Error('Choreo Personal Access Token not configured')}
          >
            <Typography variant="body1" paragraph>
              To use this plugin, you need to configure your Choreo Personal Access Token.
            </Typography>
            <Typography variant="body2" paragraph>
              <strong>Steps to configure:</strong>
            </Typography>
            <ol>
              <li>Go to <a href="https://console.choreo.dev/account/settings/token" target="_blank" rel="noopener noreferrer">Choreo Console → Account Settings → Tokens</a></li>
              <li>Create a new Personal Access Token</li>
              <li>Set the environment variable: <code>REACT_APP_CHOREO_PAT=your-token-here</code></li>
              <li>Restart your Backstage application</li>
            </ol>
          </ErrorPanel>
        </Content>
      </Page>
    );
  }

  return (
    <Page themeId="tool">
      <Header title="Choreo Integration" subtitle="Connect to your Choreo projects">
        <SupportButton>
          Simple Choreo integration using Personal Access Token authentication.
        </SupportButton>
      </Header>
      <Content>
        <ContentHeader title="Choreo Dashboard">
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
            className={classes.refreshButton}
          >
            Refresh
          </Button>
        </ContentHeader>

        <Grid container spacing={3}>
          {/* Status Card */}
          <Grid item xs={12} md={6}>
            <Card className={classes.statusCard}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Connection Status
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  {isConfigured ? (
                    <>
                      <CheckCircleIcon color="primary" />
                      <Chip 
                        label="Connected" 
                        className={classes.successChip}
                        size="small"
                      />
                    </>
                  ) : (
                    <>
                      <ErrorIcon color="error" />
                      <Chip 
                        label="Not Configured" 
                        className={classes.errorChip}
                        size="small"
                      />
                    </>
                  )}
                </Box>
                <Typography variant="body2" color="textSecondary" style={{ marginTop: 8 }}>
                  Using Personal Access Token authentication
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Projects Card */}
          <Grid item xs={12} md={6}>
            <Card className={classes.statusCard}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Projects Overview
                </Typography>
                {loading && <Progress />}
                {!loading && error && (
                  <Typography color="error" variant="body2">
                    {error}
                  </Typography>
                )}
                {!loading && !error && (
                  <Typography variant="h4" color="primary">
                    {projects.length}
                  </Typography>
                )}
                <Typography variant="body2" color="textSecondary">
                  Total projects accessible
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Projects List */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Your Choreo Projects
                </Typography>
                
                {loading && <Progress />}
                
                {error && (
                  <ErrorPanel 
                    title="Failed to load projects"
                    error={new Error(error)}
                  />
                )}
                
                {!loading && !error && projects.length === 0 && (
                  <Typography variant="body2" color="textSecondary">
                    No projects found. Make sure your PAT has the necessary permissions.
                  </Typography>
                )}
                
                {!loading && !error && projects.length > 0 && (
                  <Grid container spacing={2}>
                    {projects.map((project) => (
                      <Grid item xs={12} sm={6} md={4} key={project.id}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h6" gutterBottom>
                              {project.name}
                            </Typography>
                            {project.description && (
                              <Typography variant="body2" color="textSecondary" paragraph>
                                {project.description}
                              </Typography>
                            )}
                            {project.status && (
                              <Chip 
                                label={project.status} 
                                size="small" 
                                variant="outlined"
                              />
                            )}
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};
