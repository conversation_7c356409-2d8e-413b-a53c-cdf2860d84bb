import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Box,
  Divider,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import PersonIcon from '@material-ui/icons/Person';
import BusinessIcon from '@material-ui/icons/Business';
import EmailIcon from '@material-ui/icons/Email';
import { useChoreoAuth } from '../ChoreoAuthProvider/ChoreoAuthProvider';

const useStyles = makeStyles(theme => ({
  card: {
    maxWidth: 400,
    margin: theme.spacing(2),
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  avatar: {
    marginRight: theme.spacing(2),
    backgroundColor: theme.palette.primary.main,
  },
  infoRow: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(1),
  },
  icon: {
    marginRight: theme.spacing(1),
    color: theme.palette.text.secondary,
  },
  organizationsContainer: {
    marginTop: theme.spacing(2),
  },
  organizationChip: {
    margin: theme.spacing(0.5),
  },
}));

interface ChoreoUserInfoProps {
  compact?: boolean;
}

/**
 * Component to display Choreo user information
 */
export const ChoreoUserInfo: React.FC<ChoreoUserInfoProps> = ({ compact = false }) => {
  const classes = useStyles();
  const { authState } = useChoreoAuth();

  if (!authState.isAuthenticated || !authState.user) {
    return null;
  }

  const { user } = authState;

  if (compact) {
    return (
      <Box display="flex" alignItems="center">
        <Avatar className={classes.avatar} size="small">
          <PersonIcon />
        </Avatar>
        <Typography variant="body2">
          {user.name || user.email}
        </Typography>
      </Box>
    );
  }

  return (
    <Card className={classes.card}>
      <CardContent>
        <div className={classes.header}>
          <Avatar className={classes.avatar}>
            <PersonIcon />
          </Avatar>
          <div>
            <Typography variant="h6" component="h2">
              {user.name || 'Choreo User'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Authenticated
            </Typography>
          </div>
        </div>

        <Divider />

        <Box mt={2}>
          <div className={classes.infoRow}>
            <EmailIcon className={classes.icon} />
            <Typography variant="body2">
              {user.email}
            </Typography>
          </div>

          {user.organization?.handle && (
            <div className={classes.infoRow}>
              <BusinessIcon className={classes.icon} />
              <Typography variant="body2">
                {user.organization.handle}
              </Typography>
            </div>
          )}

          {user.organizations && user.organizations.length > 0 && (
            <div className={classes.organizationsContainer}>
              <Typography variant="subtitle2" gutterBottom>
                Organizations:
              </Typography>
              <Box>
                {user.organizations.map((org, index) => (
                  <Chip
                    key={index}
                    label={org}
                    size="small"
                    variant="outlined"
                    className={classes.organizationChip}
                  />
                ))}
              </Box>
            </div>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};
