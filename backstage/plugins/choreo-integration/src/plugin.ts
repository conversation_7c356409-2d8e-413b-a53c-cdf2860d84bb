import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
  storageApiRef,
  errorApiRef,
  identityApiRef,
} from '@backstage/core-plugin-api';

import { rootRouteRef, authCallbackRouteRef } from './routes';
import {
  choreoAuthApiRef,
  DefaultChoreoAuthApi,
  DefaultChoreoTokenStorage,
  ChoreoOAuthConfig
} from './api';

// Choreo OAuth configuration
const choreoOAuthConfig: ChoreoOAuthConfig = {
  authorizationEndpoint: 'https://console.choreo.dev/login',
  tokenEndpoint: 'https://sts.choreo.dev/oauth2/token',
  clientId: process.env.REACT_APP_CHOREO_CLIENT_ID || 'your-choreo-client-id',
  redirectUri: `${window.location.origin}/choreo-integration/auth/callback`,
  scopes: [
    'openid',
    'profile',
    'email',
    'choreo:deployment_tracks:read',
    'choreo:applications:read',
    'choreo:components:read',
    'choreo:environments:read',
    'choreo:apis:read',
    'choreo:connections:read',
    'choreo:marketplace:read',
  ],
  additionalParams: {
    prompt: 'select_account',
  },
};

export const choreoIntegrationPlugin = createPlugin({
  id: 'choreo-integration',
  routes: {
    root: rootRouteRef,
    authCallback: authCallbackRouteRef,
  },
  apis: [
    createApiFactory({
      api: choreoAuthApiRef,
      deps: {
        storageApi: storageApiRef,
        errorApi: errorApiRef,
        identityApi: identityApiRef,
      },
      factory: ({ storageApi, errorApi, identityApi }) => {
        const tokenStorage = new DefaultChoreoTokenStorage(storageApi);
        return new DefaultChoreoAuthApi(
          choreoOAuthConfig,
          tokenStorage,
          errorApi,
          identityApi,
        );
      },
    }),
  ],
});

export const ChoreoIntegrationPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoIntegrationPage',
    component: () =>
      import('./components/ChoreoIntegrationPage').then(m => m.ChoreoIntegrationPage),
    mountPoint: rootRouteRef,
  }),
);

export const ChoreoAuthCallbackPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoAuthCallbackPage',
    component: () =>
      import('./components/ChoreoAuthCallback').then(m => m.ChoreoAuthCallback),
    mountPoint: authCallbackRouteRef,
  }),
);
