import {
  createPlugin,
  createRoutableExtension,
  createApiFactory,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';
import { choreoPatApiRef, DefaultChoreoPatApi } from './api/ChoreoPatApi';

/**
 * Simple Choreo integration plugin using Personal Access Token (PAT)
 */
export const choreoIntegrationSimplePlugin = createPlugin({
  id: 'choreo-integration-simple',
  routes: {
    root: rootRouteRef,
  },
  apis: [
    createApiFactory({
      api: choreoPatApiRef,
      deps: {},
      factory: () => {
        // You can customize the base URL here if needed
        const baseUrl = process.env.REACT_APP_CHOREO_API_BASE_URL || 'https://api.choreo.dev/v1';
        return new DefaultChoreoPatApi(baseUrl);
      },
    }),
  ],
});

export const ChoreoSimpleIntegrationPage = choreoIntegrationSimplePlugin.provide(
  createRoutableExtension({
    name: 'ChoreoSimpleIntegrationPage',
    component: () =>
      import('./components/ChoreoSimplePage').then(m => m.ChoreoSimplePage),
    mountPoint: rootRouteRef,
  }),
);
