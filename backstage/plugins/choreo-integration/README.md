# Choreo Integration Plugin for Backstage

A production-ready Backstage plugin that provides seamless integration with [Choreo](https://console.choreo.dev), enabling secure authentication and management of Choreo projects, APIs, and deployments from within your Backstage developer portal.

## Features

- 🔐 **Secure OAuth 2.0 Authentication** with PKCE (Proof Key for Code Exchange)
- 🔒 **Encrypted Token Storage** using Backstage's StorageApi
- 🔄 **Automatic Token Refresh** with transparent handling of expired tokens
- 🛡️ **CSRF Protection** with state parameter validation
- 🎨 **React Components** for authentication UI and user information display
- 🚀 **API Client** with automatic token injection for Choreo REST API calls
- 📱 **Popup-based Authentication** for better user experience and security

## Installation

### 1. Configure OAuth Application in Choreo

1. Go to the [Choreo Developer Portal](https://devportal.choreo.dev) and sign in
2. In the Developer Portal header, click **Applications** and then click **+Create**
3. Enter application details:
   - **Name**: `Backstage Integration`
   - **Description**: `OAuth application for Backstage plugin integration`
4. Click **Create** to create the application
5. In the left navigation menu, click the desired environment under **Credentials** (e.g., **Development**)
6. Expand **Advanced configurations** and configure:
   - **Grant types**: Select `Authorization Code` and `Refresh Token`
   - **Public client**: Enable **Allow authentication without the client secret** (for SPA)
   - **PKCE for enhanced security**: Set to **Mandatory** for enhanced security
7. Click **Generate Credentials**
8. Note down the **Consumer Key** (this is your Client ID) for configuration

**Important**: The redirect URI will be configured as: `http://localhost:3000/choreo-integration/auth/callback` (adjust for your domain)

### 2. Environment Configuration

Add the following environment variables to your Backstage configuration:

```bash
# .env or app-config.yaml
REACT_APP_CHOREO_CLIENT_ID=your-choreo-client-id-here
```

### 3. Getting Started

Your plugin has been added to the example app in this repository, meaning you'll be able to access it by running `yarn start` in the root directory, and then navigating to [/choreo-integration](http://localhost:3000/choreo-integration).

You can also serve the plugin in isolation by running `yarn start` in the plugin directory.
This method of serving the plugin provides quicker iteration speed and a faster startup and hot reloads.
It is only meant for local development, and the setup for it can be found inside the [/dev](./dev) directory.

## Usage

### Basic Authentication

```typescript
import {
  ChoreoAuthProvider,
  ChoreoAuthButton,
  ChoreoAuthGuard,
  useChoreoAuth
} from '@internal/plugin-choreo-integration';

// Wrap your component with the auth provider
function MyComponent() {
  return (
    <ChoreoAuthProvider>
      <ChoreoAuthButton />
      <ChoreoAuthGuard>
        <ProtectedContent />
      </ChoreoAuthGuard>
    </ChoreoAuthProvider>
  );
}

// Use the auth hook
function ProtectedContent() {
  const { authState } = useChoreoAuth();

  if (authState.isAuthenticated) {
    return <div>Welcome, {authState.user?.name}!</div>;
  }

  return <div>Please sign in</div>;
}
```

### Making API Calls

```typescript
import { useApi } from '@backstage/core-plugin-api';
import { choreoAuthApiRef, DefaultChoreoApiClient } from '@internal/plugin-choreo-integration';

function MyApiComponent() {
  const choreoAuthApi = useApi(choreoAuthApiRef);

  const apiClient = new DefaultChoreoApiClient({
    baseUrl: 'https://api.choreo.dev/v1',
    authApi: choreoAuthApi,
  });

  const fetchProjects = async () => {
    try {
      const projects = await apiClient.get('/projects');
      console.log('Projects:', projects);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    }
  };

  return (
    <button onClick={fetchProjects}>
      Fetch Projects
    </button>
  );
}
```

## Security Features

- **PKCE (Proof Key for Code Exchange)** prevents authorization code interception
- **State Parameter Validation** protects against CSRF attacks
- **Token Encryption** secures stored tokens in browser storage
- **Automatic Token Refresh** handles expired tokens transparently
- **Secure Storage** uses Backstage's StorageApi instead of direct localStorage access

## Components

### ChoreoAuthProvider
Provides authentication context to child components.

### ChoreoAuthButton
A button component that handles login/logout functionality.

### ChoreoAuthGuard
Protects content behind authentication, showing login UI when not authenticated.

### ChoreoUserInfo
Displays authenticated user information.

## Troubleshooting

### Common Issues

1. **"Failed to open authentication popup"**
   - Ensure popups are allowed for your Backstage domain

2. **"Invalid redirect URI"**
   - Verify the redirect URI in Choreo matches your configuration

3. **"Authentication failed"**
   - Check the Choreo client ID configuration

## License

Apache-2.0 License
